package com.probim.bimenew.net;

import static android.os.Looper.getMainLooper;

import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.JsonObject;
import com.orhanobut.logger.Logger;
import com.probim.bimenew.activity.LoginActivity;
import com.probim.bimenew.application.BaseApp;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * Description :网络请求工具类
 * Author : Gary
 * Email  : <EMAIL> nicom
 * Date   : 2018/6/11/10:00.
 */
public class OkHttpHelper {

    private static OkHttpClient client;
    private volatile static OkHttpHelper instance;
    private Handler okHandler;

    private OkHttpHelper() {
        try {
            okHandler = new Handler(getMainLooper());
            client = new OkHttpClient();



      /*SSLUtils.SSLParams sslParams = null;
      try {
        sslParams = SSLUtils
            .getSslSocketFactory(BaseApp.getInstance().GetProbimtKey(), null, null);
      } catch (IOException e) {
        e.printStackTrace();
      }*/

      /*List<Protocol> protocols = new ArrayList<Protocol>();
      protocols.add(Protocol.HTTP_1_1);
      protocols.add(Protocol.HTTP_2);*/
            client.newBuilder()
                    //.protocols(protocols)
                    .connectTimeout(5, TimeUnit.MINUTES)//设置超时时间
                    .readTimeout(5, TimeUnit.MINUTES)//设置读取超时时间
                    .writeTimeout(5, TimeUnit.MINUTES);// 设置写入超时时间
            //.sslSocketFactory(SSLTest.getSSLContext().getSocketFactory());//ssl认证
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取实例
     */
    public static OkHttpHelper getInstance(Context context) {

        if (instance == null) {
            synchronized (OkHttpHelper.class) {
                if (instance == null) {
                    instance = new OkHttpHelper();
                }
            }

        }
        return instance;
    }

    /**
     * { * get请求
     */
    public String getSync(String url, Map<String, String> params) throws Exception {
        if (url.indexOf("?") > -1) {
            url += buildParams(params);
        } else {
            url += "?" + buildParams(params);
        }
        Request request = new Request.Builder().url(url).build();
        return getResponse(request).body().string();
    }


    /**
     * 同步post
     */
    public String postSync(String url, Map<String, String> params) throws Exception {
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : params.keySet()) {
            builder.add(key, params.get(key));
        }
        RequestBody body = builder.build();
        Request request = new Request.Builder().url(url).post(body).build();
        Response response = getResponse(request);
        return response.body().string();
    }

    /**
     * postjson串
     */
    public String postJsonSync(String url, String json) throws Exception {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(JSON, json);
        Request request = new Request.Builder().url(url).post(body).build();
        Response response = getResponse(request);
        return response.body().string();
    }

    /**
     * 异步get请求
     */
    public void getAsync(String url, Map<String, String> params, final IHttpRequestHandler handler) {
        try {
            getAsync(url, params, null, handler);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 异步get请求
     */
    public void getAsync(String url, Map<String, String> params, Map<String, String> headers, final IHttpRequestHandler handler) {
        if (url.indexOf("?") > -1) {
            url += buildParams(params);
        } else {
            url += "?" + buildParams(params);
        }
        Request.Builder builer = new Request.Builder().url(url);
        if (headers != null) {
            for (String key : headers.keySet()) {
                builer.addHeader(key, headers.get(key));
            }
        }
        final Request request = builer.build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                final String str = response.body().string();
                toLogin(str);
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                // {"Ret":-2,"Msg":"Token已过期"}
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 异步post
     */
    public void postFormBody(String url, Map<String, String> params, Map<String, String> headers, final IHttpRequestHandler handler) {
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : params.keySet()) {
            builder.add(key, params.get(key));
        }
        RequestBody body = builder.build();
        Request.Builder builer = new Request.Builder().url(url).post(body);
        if (headers != null) {
            for (String key : headers.keySet()) {
                builer.addHeader(key, headers.get(key));
            }
        }
        final Request request = builer.build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    toLogin(str);
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    public void postFormBody(String url, Map<String, String> params, IHttpRequestHandler handler) {
        try {
            postFormBody(url, params, null, handler);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 异步postjson串
     */
    public void postJsonAsync(String url, String json, final IHttpRequestHandler handler) throws Exception {
        postJsonAsync(url, json, null, handler);
    }

    /**
     * 异步postjson串
     */
    public void postJsonAsync(String url, String json, Map<String, String> headers, final IHttpRequestHandler handler) {
        Logger.t("json").e(json);
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(JSON, json);
        Request.Builder builder = new Request.Builder().url(url).post(body);
        if (headers != null) {
            for (String key : headers.keySet()) {
                builder.addHeader(key, headers.get(key));
            }
        }
        final Request request = builder.build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    toLogin(str);
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * postJsonAsync
     */
    public void postJsonAsync(String url, Map<String, String> params, final IHttpRequestHandler handler) {
        try {
            postJsonAsync(url, params, null, handler);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * postjson
     */
    public void postJsonAsync(String url, Map<String, String> params, Map<String, String> headers, final IHttpRequestHandler handler) {
        JsonObject jsonObject = new JsonObject();
        for (String key : params.keySet()) {
            jsonObject.addProperty(key, params.get(key));
        }
        try {
            postJsonAsync(url, jsonObject.toString(), headers, handler);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 上传
     */
    public void upload(String url, Map<String, File> files, Map<String, String> params, final IHttpRequestHandler handler) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        for (String key : files.keySet()) {
            builder.addFormDataPart(key, files.get(key).getName(), RequestBody.create(MediaType.parse("application/x-www-form-urlencoded; charset=utf-8"), files.get(key)));
        }
        if (params != null) {
            //其他参数
            MediaType form = MediaType.parse("application/x-www-form-urlencoded; charset=utf-8");
            RequestBody body = RequestBody.create(form, buildParams(params));
            builder.addPart(body);
        }
        RequestBody requestBody = builder.build();
        final Request request = new Request.Builder()
                //.header("Authorization", "Client-ID " + IMGUR_CLIENT_ID)
                .url(url).post(requestBody).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());

                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            handler.onResponse(call, response);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });
    }

    /**
     * 上传文件
     */
    public void upload(String url, Map<String, File> files, IHttpRequestHandler handler) {
        upload(url, files, null, handler);
    }

    /**
     * 文件下载
     */
    public void download(String url, final IHttpRequestHandler handler) {
        final Request request = new Request.Builder().url(url).build();
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            handler.onResponse(call, response);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });
    }

    /**
     * 上传文件
     */
    public void upload(String url, File file, IHttpRequestHandler handler) {
        Map<String, File> files = new HashMap<>();
        files.put(file.getName(), file);
        upload(url, files, null, handler);
    }

    /**
     * 获取响应
     */
    private Response getResponse(Request request) throws Exception {
        Response response = client.newCall(request).execute();
        return response;
    }

    /**
     * 拼接参数到url
     */
    public String buildParams(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        for (String key : params.keySet()) {
            sb.append(key + "=" + params.get(key) + "&");
        }
        String s = "";
        if (sb.length() > 0) {
            s = sb.substring(0, sb.length() - 1);
        } else {
            s = sb.toString();
        }
        return s;
    }

    /**
     * 异步FormData上传
     */
    public void postFormData(String url, HashMap<String, String> params, final IHttpRequestHandler handler) {

        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);

        if (params != null) {
            for (String key : params.keySet()) {
                builder.addFormDataPart(key, params.get(key));
            }
        }

        //构建
        MultipartBody multipartBody = builder.build();

        Request request = new Request.Builder().url(url).post(multipartBody).build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    toLogin(str);
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 上传照片
     */
    public void uploadPhoto(String url, List<File> files, Map<String, String> params, final IHttpRequestHandler handler) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        for (int i = 0; i < files.size(); i++) {
            builder.addFormDataPart("", files.get(i).getName(), RequestBody.create(MediaType.parse("image/*"), files.get(i)));
        }

        JsonObject jsonObject = new JsonObject();

        for (String key : params.keySet()) {
            builder.addFormDataPart(key, params.get(key));
        }


  /*  MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    RequestBody body = RequestBody.create(JSON, jsonObject.toString());
    builder.addPart(body);*/

        RequestBody requestBody = builder.build();
        final Request request = new Request.Builder().url(url).post(requestBody).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 上传音频
     */
    public void uploadAudio(String url, List<File> files, Map<String, String> params, final IHttpRequestHandler handler) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        for (int i = 0; i < files.size(); i++) {
            String keyName = "android_" + System.currentTimeMillis() + new Random().nextInt();
            builder.addFormDataPart(keyName, files.get(i).getName(), RequestBody.create(MediaType.parse("application/octet-stream"), files.get(i)));
        }

        JsonObject jsonObject = new JsonObject();

        for (String key : params.keySet()) {
            builder.addFormDataPart(key, params.get(key));
        }


  /*  MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    RequestBody body = RequestBody.create(JSON, jsonObject.toString());
    builder.addPart(body);*/

        RequestBody requestBody = builder.build();
        final Request request = new Request.Builder().url(url).post(requestBody).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 上传数组
     */
    private void uploadArray(String url, Map<String, String> params, final IHttpRequestHandler handler) {

        MultipartBody.Builder builder = new MultipartBody.Builder();

        if (params != null && !params.isEmpty()) {
            for (String key : params.keySet()) {
                builder.addPart(Headers.of("Content-Disposition", "form-data; name=\"" + key.replaceAll("\\[\\d+\\]", "") + "\""), RequestBody.create(null, params.get(key)));
            }
        }
        RequestBody requestBody = builder.build();
        final Request request = new Request.Builder().url(url).post(requestBody).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 异步postjson串
     */
    public void postJsonString(String url, String json, final IHttpRequestHandler handler) {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(JSON, json);
        Request.Builder builder = new Request.Builder().url(url).post(body);

        final Request request = builder.build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    toLogin(str);
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 上传照片
     */
    public void uploadSchedualPhoto(String url, List<File> files, List<String> nameList, Map<String, String> params, final IHttpRequestHandler handler) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        for (int i = 0; i < files.size(); i++) {
            builder.addFormDataPart(nameList.get(i), nameList.get(i), RequestBody.create(MediaType.parse("image/*"), files.get(i)));
        }

        JsonObject jsonObject = new JsonObject();

        for (String key : params.keySet()) {
            builder.addFormDataPart(key, params.get(key));
        }


  /*  MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    RequestBody body = RequestBody.create(JSON, jsonObject.toString());
    builder.addPart(body);*/

        RequestBody requestBody = builder.build();
        final Request request = new Request.Builder().url(url).post(requestBody).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 上传照片
     */
    public void uploadFullviewPhoto(String url, File file, Map<String, String> params, Map<String, String> urlParams, String photoName, final IHttpRequestHandler handler) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        builder.addFormDataPart("Files", photoName, RequestBody.create(MediaType.parse("image/*"), file));

        JsonObject jsonObject = new JsonObject();

        for (String key : params.keySet()) {
            builder.addFormDataPart(key, params.get(key));
        }


  /*  MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    RequestBody body = RequestBody.create(JSON, jsonObject.toString());
    builder.addPart(body);*/

        if (url.indexOf("?") > -1) {
            url += buildParams(urlParams);
        } else {
            url += "?" + buildParams(urlParams);
        }
        RequestBody requestBody = builder.build();
        final Request request = new Request.Builder().url(url).post(requestBody).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    toLogin(str);
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 上传照片
     */
    public void postWithUrlParams(String url, Map<String, String> urlParams, final IHttpRequestHandler handler) {


        FormBody.Builder builder = new FormBody.Builder();
        if (url.indexOf("?") > -1) {
            url += buildParams(urlParams);
        } else {
            url += "?" + buildParams(urlParams);
        }
        RequestBody requestBody = builder.build();
        final Request request = new Request.Builder().url(url).post(requestBody).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 上传照片
     */
    public void uploadIssuePhoto(String url, List<File> files, Map<String, String> params, final IHttpRequestHandler handler) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        for (String key : params.keySet()) {
            builder.addFormDataPart(key, params.get(key));
        }
        for (int i = 0; i < files.size(); i++) {
            builder.addFormDataPart("Files", files.get(i).getName(), RequestBody.create(MediaType.parse("image/*"), files.get(i)));
        }

        JsonObject jsonObject = new JsonObject();




  /*  MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    RequestBody body = RequestBody.create(JSON, jsonObject.toString());
    builder.addPart(body);*/

        RequestBody requestBody = builder.build();
        final Request request = new Request.Builder().url(url).post(requestBody).build();
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(final Call call, final IOException e) {
                e.printStackTrace();
                okHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        handler.onFailure(call, e);
                    }
                });
            }

            @Override
            public void onResponse(final Call call, final Response response) throws IOException {
                Log.d("---wyf---", "response: " + response.body().string());;

                if (handler instanceof IHttpRequestHandler1) {
                    final String str = response.body().string();
                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                ((IHttpRequestHandler1) handler).onResponse(call, response, str);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                } else {

                    okHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                handler.onResponse(call, response);
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 跳转登录界面
     *
     * @param str
     */
    private void toLogin(String str) {
        if (!TextUtils.isEmpty(str)) {
        if (str.startsWith("{") && str.endsWith("}")){
                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(str);
                    if (jsonObject.has("Ret")) {
                        int ret = jsonObject.getInt("Ret");
                        if (ret == -2) {
                            Intent intent =new Intent(BaseApp.getContext(), LoginActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            BaseApp.getContext().startActivity(intent);
                        }else if (ret == -1){
                            String msg = jsonObject.getString("Msg");
                            if ("Token错误".equals(msg)){
                                Intent intent =new Intent(BaseApp.getContext(), LoginActivity.class);
                                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                BaseApp.getContext().startActivity(intent);
                            }
                        }
                    }
                } catch (JSONException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }
}
